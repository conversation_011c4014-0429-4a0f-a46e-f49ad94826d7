const http = require('http');
const app = require('./src/app');
const port = process.env.PORT || 3000;
const server = http.createServer(app);

// Handle server errors
server.on('error', (error) => {
    if (error.syscall !== 'listen') {
        throw error;
    }

    switch (error.code) {
        case 'EACCES':
            console.error(`Port ${port} requires elevated privileges`);
            process.exit(1);
            break;
        case 'EADDRINUSE':
            console.error(`Port ${port} is already in use`);
            process.exit(1);
            break;
        default:
            throw error;
    }
});

// Start server immediately to handle CORS preflight requests
const startServer = async () => {
    try {
        // Start server immediately to handle CORS preflight requests
        server.listen(port, () => {
            console.log(`Server is running on port ${port}`);
            console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
            if (process.env.NODE_ENV === 'production') {
                console.log('CORS enabled for:');
                console.log('- https://admin.dabgarsamaj.in');
                console.log('- https://api.dabgarsamaj.in');
                console.log('- https://dabgarsamaj.in');
            } else {
                console.log('CORS enabled for all origins (development mode)');
            }
        });

        // Handle graceful shutdown
        process.on('SIGTERM', () => {
            console.log('Received SIGTERM. Performing graceful shutdown...');
            server.close(() => {
                console.log('Server closed. Exiting process.');
                process.exit(0);
            });
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
};

startServer();
