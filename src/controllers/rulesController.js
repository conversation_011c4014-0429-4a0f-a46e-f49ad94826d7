const { validationResult } = require('express-validator');
const db = require('../db');
require('dotenv').config();

class RulesController {
    // Get rules
    async getRules(req, res) {
        try {
            const connection = await db.getConnection();

            try {
                const [results] = await connection.query('SELECT * FROM rules LIMIT 1');
                connection.release();

                if (results.length === 0) {
                    return res.status(404).json({ status: false, message: 'No rules found.' });
                }

                const rule = results[0];

                res.status(200).json({
                    status: true,
                    message: 'Rules found successful',
                    data: {
                        id: rule.iRulesId,
                        title: rule.vTitle,
                        description: rule.tDescription
                    }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Update rules
    async updateRule(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }

        db.getConnection((err, connection) => {
            if (err) {
                return res.status(500).json({
                    status: false,
                    message: 'Database connection error'
                });
            }

            const { rule_id, title, description } = req.body;
            connection.query(
                'UPDATE rules SET vTitle = ?, tDescription = ?, dModifiedDate = NOW() WHERE iRulesId = ?',
                [title, description, rule_id],
                (err, results) => {
                    connection.release();
                    if (err) {
                        return res.status(500).json({
                            status: false,
                            message: 'Database error'
                        });
                    }

                    if (results.affectedRows === 0) {
                        return res.status(404).json({ status: false, message: 'Rule not found.' });
                    }

                    res.status(200).json({
                        status: true,
                        message: 'Rule updated successfully'
                    });
                }
            );
        });
    }
}

module.exports = RulesController;
