const db = require('../db');

class DashboardController {
    async getDashboardDetails(req, res) {
        try {
            const connection = await db.getConnection();

            try {
                // Queries for dashboard details
                const totalMembersQuery = `SELECT COUNT(*) AS total_members FROM member WHERE eStatus = 'Active' AND iIsDeleted = 0`;
                const totalActiveMaleMembersQuery = `SELECT COUNT(*) AS total_active_male_members
                    FROM member
                    WHERE eStatus = 'Active'
                    AND eMartialStatus IN ('Single', 'Divorced')
                    AND eGender = 'Male'
                    AND TIMESTAMPDIFF(YEAR, dDob, CURDATE()) > 18
                    AND iIsDeleted = 0`;

                const totalActiveFemaleMembersQuery = `SELECT COUNT(*) AS total_active_female_members
                    FROM member
                    WHERE eStatus = 'Active'
                    AND eMartialStatus IN ('Single', 'Divorced')
                    AND eGender = 'Female'
                    AND TIMESTAMPDIFF(YEAR, dDob, CURDATE()) > 18
                    AND iIsDeleted = 0`;

                const totalCitiesQuery = `SELECT COUNT(*) AS total_cities FROM city WHERE eStatus = 'Active' AND iIsDeleted = 0`;
                const totalStatesQuery = `SELECT COUNT(*) AS total_states FROM state WHERE eStatus = 'Active' AND iIsDeleted = 0`;

                // Queries for age-wise chart
                const ageAbove50Query = `SELECT COUNT(*) AS count FROM member WHERE TIMESTAMPDIFF(YEAR, dDob, CURDATE()) > 50 AND iIsDeleted = 0`;
                const age31To50Query = `SELECT COUNT(*) AS count FROM member WHERE TIMESTAMPDIFF(YEAR, dDob, CURDATE()) BETWEEN 31 AND 50 AND iIsDeleted = 0`;
                const age15To30Query = `SELECT COUNT(*) AS count FROM member WHERE TIMESTAMPDIFF(YEAR, dDob, CURDATE()) BETWEEN 15 AND 30 AND iIsDeleted = 0`;
                const age0To14Query = `SELECT COUNT(*) AS count FROM member WHERE TIMESTAMPDIFF(YEAR, dDob, CURDATE()) BETWEEN 0 AND 14 AND iIsDeleted = 0`;

                // Execute all queries in parallel using Promise.all with async/await
                const [
                    [totalMembersResults],
                    [totalActiveMaleMembersResults],
                    [totalActiveFemaleMembersResults],
                    [totalCitiesResults],
                    [totalStatesResults],
                    [ageAbove50Results],
                    [age31To50Results],
                    [age15To30Results],
                    [age0To14Results]
                ] = await Promise.all([
                    connection.query(totalMembersQuery),
                    connection.query(totalActiveMaleMembersQuery),
                    connection.query(totalActiveFemaleMembersQuery),
                    connection.query(totalCitiesQuery),
                    connection.query(totalStatesQuery),
                    connection.query(ageAbove50Query),
                    connection.query(age31To50Query),
                    connection.query(age15To30Query),
                    connection.query(age0To14Query)
                ]);

                connection.release();

                // Extract results
                const totalMembersResult = totalMembersResults[0].total_members;
                const totalActiveMaleMembersResult = totalActiveMaleMembersResults[0].total_active_male_members;
                const totalActiveFemaleMembersResult = totalActiveFemaleMembersResults[0].total_active_female_members;
                const totalCitiesResult = totalCitiesResults[0].total_cities;
                const totalStatesResult = totalStatesResults[0].total_states;
                const ageAbove50Result = ageAbove50Results[0].count;
                const age31To50Result = age31To50Results[0].count;
                const age15To30Result = age15To30Results[0].count;
                const age0To14Result = age0To14Results[0].count;

                // Calculate percentages for age groups
                const totalMembers = totalMembersResult;
                const ageWiseChart = [
                    {
                        age_group: '50+ years',
                        percentage: totalMembers > 0 ? ((ageAbove50Result / totalMembers) * 100).toFixed(2) : '0.00'
                    },
                    {
                        age_group: '31-50 years',
                        percentage: totalMembers > 0 ? ((age31To50Result / totalMembers) * 100).toFixed(2) : '0.00'
                    },
                    {
                        age_group: '15-30 years',
                        percentage: totalMembers > 0 ? ((age15To30Result / totalMembers) * 100).toFixed(2) : '0.00'
                    },
                    {
                        age_group: '0-14 years',
                        percentage: totalMembers > 0 ? ((age0To14Result / totalMembers) * 100).toFixed(2) : '0.00'
                    }
                ];

                // Send response
                res.status(200).json({
                    status: true,
                    message: 'Dashboard details fetched successfully',
                    data: {
                        total_members: totalMembersResult,
                        total_active_male_members: totalActiveMaleMembersResult,
                        total_active_female_members: totalActiveFemaleMembersResult,
                        total_cities: totalCitiesResult,
                        total_states: totalStatesResult,
                        age_wise_chart: ageWiseChart
                    }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = DashboardController;
