const db = require('../db');

class StateController {
    async getStateList(req, res) {
        const { status } = req.query;

        try {
            const connection = await db.getConnection();

            try {
                let query = 'SELECT iStateId, vStateName, eStatus FROM state WHERE iIsDeleted = 0';
                const queryParams = [];

                if (status) {
                    if (status === 'Active' || status === 'Inactive') {
                        query += ' AND eStatus = ?';
                        queryParams.push(status);
                    }
                }

                query += ' ORDER BY vStateName ASC';

                const [results] = await connection.query(query, queryParams);
                connection.release();

                // Format the response data
                const formattedData = results.map(state => ({
                    state_id: state.iStateId,
                    state_name: state.vStateName,
                    state_code: state.vStateCode,
                    status: state.eStatus
                }));

                return res.status(200).json({
                    status: true,
                    message: 'States retrieved successfully',
                    data: {
                        states: formattedData
                    }
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    async addState(req, res) {
        const { state_name, status } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                const query = 'INSERT INTO state (vStateName, eStatus) VALUES (?, ?)';
                const [results] = await connection.execute(query, [state_name, status]);
                connection.release();

                return res.status(201).json({
                    status: true,
                    message: 'State added successfully',
                    data: {
                        state_id: results.insertId,
                        state_name,
                        state_code: state_name ? state_name.substring(0, 2).toUpperCase() : null,
                        status
                    }
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    async updateState(req, res) {
        const { state_id, state_name, status } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                const query = 'UPDATE state SET vStateName = ?, eStatus = ? WHERE iStateId = ?';
                await connection.query(query, [state_name, status, state_id]);
                connection.release();

                return res.status(200).json({
                    status: true,
                    message: 'State updated successfully'
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    async deleteState(req, res) {
        const { state_id } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                const query = 'UPDATE state SET iIsDeleted = 1 WHERE iStateId = ?';
                await connection.execute(query, [state_id]);
                connection.release();

                return res.status(200).json({
                    status: true,
                    message: 'State deleted successfully'
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = StateController;
