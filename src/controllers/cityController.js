const { body, validationResult } = require('express-validator');
const db = require('../db');
require('dotenv').config();

class CityController {
    // Get Cities function
    async getCities(req, res) {
        try {
            const { status, state_id } = req.query;

            if (status && status !== 'Active' && status !== 'Inactive') {
                return res.status(400).json({ status: false, message: 'Invalid status value.' });
            }

            const connection = await db.getConnection();

            try {
                let query = `
                    SELECT city.*, state.vStateName
                    FROM city
                    JOIN state ON city.iStateId = state.iStateId
                    WHERE city.iIsDeleted = 0
                `;
                const queryParams = [];

                if (status) {
                    query += ' AND city.eStatus = ?';
                    queryParams.push(status);
                }

                if (state_id) {
                    query += ' AND city.iStateId = ?';
                    queryParams.push(state_id);
                }

                const [results] = await connection.query(query, queryParams);
                connection.release();

                if (results.length === 0) {
                    return res.status(200).json({ status: false, message: 'No cities found.' });
                }

                const cities = results.map(city => ({
                    city_id: city.iCityId,
                    state_id: city.iStateId,
                    state_name: city.vStateName,
                    city_name: city.vCityName,
                    city_code: city.vCityCode,
                    status: city.eStatus
                }));

                res.status(200).json({
                    status: true,
                    message: 'Cities found successfully',
                    data: { cities }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Add City function
    async addCity(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }
        const { state_id, city_name, city_code, status } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                // Check if city code is unique
                const [existingResults] = await connection.execute('SELECT * FROM city WHERE vCityCode = ?', [city_code]);

                if (existingResults.length > 0) {
                    connection.release();
                    return res.status(400).json({ status: false, message: 'City code already exists' });
                }

                // Add new city
                const [results] = await connection.execute(
                    'INSERT INTO city (iStateId, vCityName, vCityCode, eStatus) VALUES (?, ?, ?, ?)',
                    [state_id, city_name, city_code, status]
                );
                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'City added successfully'
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({ status: false, message: 'Database error', error: queryErr.message });
            }
        } catch (err) {
            return res.status(500).json({ status: false, message: 'Database connection error' });
        }
    }

     // Update City function
     async updateCity(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }
        const { state_id, city_id, city_name, city_code, status } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                // Update city
                const [results] = await connection.query(
                    'UPDATE city SET iStateId = ?, vCityName = ?, vCityCode = ?, eStatus = ? WHERE iCityId = ?',
                    [state_id, city_name, city_code, status, city_id]
                );
                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'City updated successfully'
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({ status: false, message: 'Database error', error: queryErr.message });
            }
        } catch (err) {
            return res.status(500).json({ status: false, message: 'Database connection error' });
        }
    }

    // Delete City function
    async deleteCity(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }
        const { city_id } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                // Delete city (soft delete)
                const [results] = await connection.query('UPDATE city SET iIsDeleted = 1 WHERE iCityId = ?', [city_id]);
                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'City deleted successfully'
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({ status: false, message: 'Database error', error: queryErr.message });
            }
        } catch (err) {
            return res.status(500).json({ status: false, message: 'Database connection error' });
        }
    }
}

module.exports = CityController;
