const jwt = require('jsonwebtoken');
const db = require('../db');
require('dotenv').config();

class AdminController {
    async login(req, res) {
        const { username, password } = req.body;

        try {
            // Get connection from pool
            const connection = await db.getConnection();

            try {
                // Query for admin user
                const [results] = await connection.query(
                    'SELECT * FROM admin WHERE vUserName = ? AND eStatus = "Active"',
                    [username]
                );

                if (results.length === 0) {
                    connection.release();
                    return res.status(400).json({
                        status: false,
                        message: 'Invalid credentials'
                    });
                }

                const admin = results[0];

                // Check password
                const isMatch = password === admin.vPassword;
                if (!isMatch) {
                    connection.release();
                    return res.status(400).json({
                        status: false,
                        message: 'Invalid credentials'
                    });
                }

                // Generate token
                const token = jwt.sign({ id: admin.iAdminId }, process.env.JWT_SECRET, { expiresIn: '2d' });
                const expiryDate = new Date();
                expiryDate.setDate(expiryDate.getDate() + 2);

                // Insert session
                await connection.query(
                    'INSERT INTO admin_session (iAdminId, tToken, dExpiryDate) VALUES (?, ?, ?)',
                    [admin.iAdminId, token, expiryDate]
                );

                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'Login successful',
                    data: { id: admin.iAdminId, token }
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

// DO NOT set res.header('Access-Control-Allow-Origin', ...) anywhere

module.exports = AdminController;
