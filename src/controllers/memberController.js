const { body, validationResult } = require('express-validator');
const db = require('../db');
const crypto = require('crypto');
const moment = require('moment');
require('dotenv').config();

class MemberController {
    // Helper method to get children for a member
    async getChildrenForMember(connection, childrenIds) {
        if (!childrenIds || childrenIds.trim() === '') {
            return [];
        }

        try {
            const childrenIdArray = childrenIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            if (childrenIdArray.length === 0) {
                return [];
            }

            const placeholders = childrenIdArray.map(() => '?').join(',');
            const childrenQuery = `SELECT iMemberId, vMemberName, (YEAR(CURDATE()) - YEAR(dDob)) AS age, eGender FROM member WHERE iIsDeleted = 0 AND iMemberId IN (${placeholders})`;

            const [childrenResults] = await connection.query(childrenQuery, childrenIdArray);

            return childrenResults.map(child => ({
                id: child.iMemberId,
                name: child.vMemberName,
                age: child.age,
                gender: child.eGender
            }));
        } catch (err) {
            return [];
        }
    }

    // Helper method to format member data
    formatMemberData(member, children = []) {
        return {
            member_id: member.iMemberId,
            member_code: member.vMemberCode,
            member_name: member.vMemberName,
            member_name_gj: member.vMemberNameGj,
            member_name_hi: member.vMemberNameHi,
            nick_name: member.vNickName,
            nick_name_gj: member.vNickNameGj,
            nick_name_hi: member.vNickNameHi,
            mobile_number: member.vMobileNumber,
            alternate_number: member.vAlternateMobileNumber,
            father_id: member.iFatherId,
            father_name: member.father_name,
            mother_id: member.iMotherId,
            mother_name: member.mother_name,
            spouse_id: member.iSpouseId,
            spouse_name: member.spouse_name,
            gender: member.eGender,
            address: member.tAddress,
            state_id: member.iStateId,
            state: member.vStateName,
            city_id: member.iCityId,
            city: member.vCityName,
            pincode: member.vPincode,
            panch_city: member.vPanchCity,
            email: member.vEmail,
            dob: member.dDob ? moment(member.dDob).format('YYYY-MM-DD') : null,
            age: member.dDob ? moment().diff(moment(member.dDob), 'years') : null,
            martial_status: member.eMartialStatus,
            graduation: member.vGraduation,
            school_collage_name: member.vSchoolCollegeName,
            hsc: member.vHSC,
            ssc: member.vSSC,
            occupation: member.vOccupation,
            company_ferm_name: member.vCompanyFermName,
            experience: member.vExperience,
            other_business: member.vOtherBusiness,
            physically_challenged: member.iIsPhysicallyChallenged,
            physically_challenged_details: member.vPhysicallyChallengedDetails,
            member_status: member.eStatus,
            childrens: children
        };
    }

    // Get Members function
    async getMembers(req, res) {
        try {
            const { page = 1, language = 'en', member_name, city_id, min_age, max_age, martial_status } = req.query;
            const connection = await db.getConnection();

            try {
                // Build query
                let query = `
                        SELECT member.*,
                               state.vStateName,
                               city.vCityName,
                               father.vMemberName AS father_name,
                               mother.vMemberName AS mother_name,
                               spouse.vMemberName AS spouse_name
                        FROM member
                        LEFT JOIN state ON member.iStateId = state.iStateId
                        LEFT JOIN city ON member.iCityId = city.iCityId
                        LEFT JOIN member AS father ON member.iFatherId = father.iMemberId
                        LEFT JOIN member AS mother ON member.iMotherId = mother.iMemberId
                        LEFT JOIN member AS spouse ON member.iSpouseId = spouse.iMemberId
                        WHERE member.iIsDeleted = 0
                    `;

                const queryParams = [];

                // Add filters
                if (member_name && member_name.trim() !== '') {
                    query += ` AND (member.vMemberName LIKE ? OR member.vNickName LIKE ? OR member.vMemberNameGj LIKE ? OR member.vNickNameGj LIKE ? OR member.vMemberNameHi LIKE ? OR member.vNickNameHi LIKE ?)`;
                    queryParams.push(`%${member_name}%`, `%${member_name}%`, `%${member_name}%`, `%${member_name}%`, `%${member_name}%`, `%${member_name}%`);
                }

                if (city_id && city_id.trim() !== '') {
                    query += ` AND member.iCityId = ?`;
                    queryParams.push(city_id);
                }

                if (martial_status && martial_status.trim() !== '') {
                    query += ` AND member.eMartialStatus = ?`;
                    queryParams.push(martial_status);
                }

                if ((min_age && min_age !== 'null') || (max_age && max_age !== 'null')) {
                    query += ` AND (YEAR(CURDATE()) - YEAR(member.dDob)) BETWEEN ? AND ?`;
                    queryParams.push(min_age || 0, max_age || 150);
                }

                // Pagination
                const limit = 20;
                const offset = (page - 1) * limit;
                query += ` ORDER BY member.iMemberId DESC LIMIT ? OFFSET ?`;
                queryParams.push(limit, offset);

                // Execute main query
                const [results] = await connection.query(query, queryParams);

                if (results.length === 0) {
                    connection.release();
                    return res.status(200).json({ status: false, message: 'No members found.' });
                }

                // Helper function to get children
                const getChildrenForMember = async (connection, childrenIds) => {
                    if (!childrenIds || childrenIds.trim() === '') {
                        return [];
                    }

                    try {
                        const childrenIdArray = childrenIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
                        if (childrenIdArray.length === 0) {
                            return [];
                        }

                        const placeholders = childrenIdArray.map(() => '?').join(',');
                        const childrenQuery = `SELECT iMemberId, vMemberName, (YEAR(CURDATE()) - YEAR(dDob)) AS age, eGender FROM member WHERE iIsDeleted = 0 AND iMemberId IN (${placeholders})`;

                        const [childrenResults] = await connection.query(childrenQuery, childrenIdArray);

                        return childrenResults.map(child => ({
                            id: child.iMemberId,
                            name: child.vMemberName,
                            age: child.age,
                            gender: child.eGender
                        }));
                    } catch (err) {
                        return [];
                    }
                };

                // Helper function to format member data
                const formatMemberData = (member, children = []) => {
                    return {
                        member_id: member.iMemberId,
                        member_code: member.vMemberCode,
                        member_name: member.vMemberName,
                        member_name_gj: member.vMemberNameGj,
                        member_name_hi: member.vMemberNameHi,
                        nick_name: member.vNickName,
                        nick_name_gj: member.vNickNameGj,
                        nick_name_hi: member.vNickNameHi,
                        mobile_number: member.vMobileNumber,
                        alternate_number: member.vAlternateMobileNumber,
                        father_id: member.iFatherId,
                        father_name: member.father_name,
                        mother_id: member.iMotherId,
                        mother_name: member.mother_name,
                        spouse_id: member.iSpouseId,
                        spouse_name: member.spouse_name,
                        gender: member.eGender,
                        address: member.tAddress,
                        state_id: member.iStateId,
                        state: member.vStateName,
                        city_id: member.iCityId,
                        city: member.vCityName,
                        pincode: member.vPincode,
                        panch_city: member.vPanchCity,
                        email: member.vEmail,
                        dob: member.dDob ? moment(member.dDob).format('YYYY-MM-DD') : null,
                        age: member.dDob ? moment().diff(moment(member.dDob), 'years') : null,
                        martial_status: member.eMartialStatus,
                        graduation: member.vGraduation,
                        school_collage_name: member.vSchoolCollegeName,
                        hsc: member.vHSC,
                        ssc: member.vSSC,
                        occupation: member.vOccupation,
                        company_ferm_name: member.vCompanyFermName,
                        experience: member.vExperience,
                        other_business: member.vOtherBusiness,
                        physically_challenged: member.iIsPhysicallyChallenged,
                        physically_challenged_details: member.vPhysicallyChallengedDetails,
                        member_status: member.eStatus,
                        childrens: children
                    };
                };

                const membersWithChildren = await Promise.all(
                    results.map(async (member) => {
                        const children = await getChildrenForMember(connection, member.vChildrenIds);
                        return formatMemberData(member, children);
                    })
                );

                // Get member statistics
                const [
                    [totalMembersResults],
                    [activeMembersResults],
                    [naatbaharMembersResults]
                ] = await Promise.all([
                    connection.query(`SELECT COUNT(*) AS total_members FROM member WHERE eStatus NOT IN ('Inactive', 'Death')`),
                    connection.query(`SELECT COUNT(*) AS active_members FROM member WHERE eStatus = 'Active'`),
                    connection.query(`SELECT COUNT(*) AS naatbahar_members FROM member WHERE eStatus = 'NaatBahar'`)
                ]);

                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'Members found successfully',
                    data: {
                        current_page: parseInt(page),
                        per_page: limit,
                        total_pages: Math.ceil(results.length / limit),
                        total_items: results.length,
                        members: membersWithChildren,
                        members_info: {
                            total_members: totalMembersResults[0].total_members,
                            active_members: activeMembersResults[0].active_members,
                            naatbahar_members: naatbaharMembersResults[0].naatbahar_members
                        }
                    }
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Add Member function
    async addMember(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }

        const {
            member_name, member_name_gj, member_name_hi, nick_name, nick_name_gj, nick_name_hi, photo, mobile_number,
            alternate_mobile_number, address, state_id, city_id, pincode, panch_city, father_id, mother_id, spouse_id,
            email, dob, martial_status, graduation, school_college_name, hsc, ssc, occupation, company_ferm_name,
            experience, other_business, children_ids, status, is_physically_challenged, physically_challenged_details, gender
        } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                // Generate unique alphanumeric string of length 10 for vMemberCode
                const vMemberCode = crypto.randomBytes(5).toString('hex');

                const query = `
                    INSERT INTO member (
                        vMemberName, vMemberNameHi, vMemberNameGj, vNickName, vNickNameHi, vNickNameGj, vPhoto, vMobileNumber,
                        vAlternateMobileNumber, tAddress, iStateId, iCityId, vPincode, vPanchCity, iFatherId, iMotherId, iSpouseId,
                        vEmail, dDob, eMartialStatus, vGraduation, vSchoolCollegeName, vHSC, vSSC, vOccupation, vCompanyFermName,
                        vExperience, vOtherBusiness, vChildrenIds, eStatus, iIsPhysicallyChallenged, vPhysicallyChallengedDetails, eGender, vMemberCode
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;

                const queryParams = [
                    member_name, member_name_hi, member_name_gj, nick_name, nick_name_hi, nick_name_gj, photo, mobile_number,
                    alternate_mobile_number, address, state_id, city_id, pincode, panch_city, father_id, mother_id, spouse_id,
                    email, dob, martial_status, graduation, school_college_name, hsc, ssc, occupation, company_ferm_name,
                    experience, other_business, children_ids, status, is_physically_challenged, physically_challenged_details, gender, vMemberCode
                ];

                const [results] = await connection.query(query, queryParams);

                // Call the MySQL procedure after successfully adding the member
                try {
                    await connection.query('CALL process_member_updates()');
                } catch (procedureErr) {
                    // Continue anyway as the member was added successfully
                }

                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'Member added successfully',
                    data: { member_id: results.insertId, member_code: vMemberCode }
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Update Member function
    async updateMember(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }

        const {
            member_id, member_name, member_name_gj, member_name_hi, nick_name, nick_name_gj, nick_name_hi, photo, mobile_number,
            alternate_mobile_number, address, state_id, city_id, pincode, panch_city, father_id, mother_id, spouse_id,
            email, dob, martial_status, graduation, school_college_name, hsc, ssc, occupation, company_ferm_name,
            experience, other_business, children_ids, status, is_physically_challenged, physically_challenged_details, gender
        } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                const query = `
                    UPDATE member SET
                        vMemberName = ?, vMemberNameHi = ?, vMemberNameGj = ?, vNickName = ?, vNickNameHi = ?, vNickNameGj = ?, vPhoto = ?, vMobileNumber = ?,
                        vAlternateMobileNumber = ?, tAddress = ?, iStateId = ?, iCityId = ?, vPincode = ?, vPanchCity = ?, iFatherId = ?, iMotherId = ?, iSpouseId = ?,
                        vEmail = ?, dDob = ?, eMartialStatus = ?, vGraduation = ?, vSchoolCollegeName = ?, vHSC = ?, vSSC = ?, vOccupation = ?, vCompanyFermName = ?,
                        vExperience = ?, vOtherBusiness = ?, vChildrenIds = ?, eStatus = ?, iIsPhysicallyChallenged = ?, vPhysicallyChallengedDetails = ?, eGender = ?
                    WHERE iMemberId = ?
                `;

                const queryParams = [
                    member_name, member_name_hi, member_name_gj, nick_name, nick_name_hi, nick_name_gj, photo, mobile_number,
                    alternate_mobile_number, address, state_id, city_id, pincode, panch_city, father_id, mother_id, spouse_id,
                    email, dob, martial_status, graduation, school_college_name, hsc, ssc, occupation, company_ferm_name,
                    experience, other_business, children_ids, status, is_physically_challenged, physically_challenged_details, gender, member_id
                ];

                await connection.query(query, queryParams);
                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'Member updated successfully'
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Delete Member function
    async deleteMember(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }

        const { member_id } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                // Execute all delete/update operations in sequence
                const deleteQuery = `UPDATE member SET iIsDeleted = 1 WHERE iMemberId = ?`;
                const updateFatherQuery = `UPDATE member SET iFatherId = NULL WHERE iFatherId = ?`;
                const updateMotherQuery = `UPDATE member SET iMotherId = NULL WHERE iMotherId = ?`;
                const updateSpouseQuery = `UPDATE member SET iSpouseId = NULL WHERE iSpouseId = ?`;

                // Execute all queries in sequence
                await connection.query(deleteQuery, [member_id]);
                await connection.query(updateFatherQuery, [member_id]);
                await connection.query(updateMotherQuery, [member_id]);
                await connection.query(updateSpouseQuery, [member_id]);

                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'Member deleted and related records updated successfully'
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = MemberController;
