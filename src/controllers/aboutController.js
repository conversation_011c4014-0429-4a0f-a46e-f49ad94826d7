const { validationResult } = require('express-validator');
const db = require('../db');
require('dotenv').config();

class AboutController {
    // Get about information
    async getAbout(req, res) {
        try {
            const connection = await db.getConnection();

            try {
                const [results] = await connection.query('SELECT * FROM about LIMIT 1');
                connection.release();

                if (results.length === 0) {
                    return res.status(404).json({ status: false, message: 'No about information found.' });
                }

                const about = results[0];

                res.status(200).json({
                    status: true,
                    message: 'About information found successfully',
                    data: {
                        id: about.iAboutId,
                        title: about.vTitle,
                        description: about.tDescription
                    }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Update about information
    async updateAbout(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }

        db.getConnection((err, connection) => {
            if (err) {
                return res.status(500).json({
                    status: false,
                    message: 'Database connection error'
                });
            }

            const { about_id, title, description } = req.body;
            connection.query(
                'UPDATE about SET vTitle = ?, tDescription = ?, dModifiedDate = NOW() WHERE iAboutId = ?',
                [title, description, about_id],
                (err, results) => {
                    connection.release();
                    if (err) {
                        return res.status(500).json({
                            status: false,
                            message: 'Database error'
                        });
                    }

                    if (results.affectedRows === 0) {
                        return res.status(404).json({ status: false, message: 'About information not found.' });
                    }

                    res.status(200).json({
                        status: true,
                        message: 'About information updated successfully'
                    });
                }
            );
        });
    }
}

module.exports = AboutController;
