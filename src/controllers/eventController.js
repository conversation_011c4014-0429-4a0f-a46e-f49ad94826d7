const fs = require('fs');
const { validationResult } = require('express-validator');
const db = require('../db');
const multer = require('multer');
const moment = require('moment');
const path = require('path');
require('dotenv').config();

// Configure multer for file upload
const allowedTypes = ['jpeg', 'jpg', 'png'];

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/events/'); // Specify the directory to save the uploaded files
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + path.extname(file.originalname)); // Generate a unique filename
    }
});

const fileFilter = (req, file, cb) => {
    try {
        const fileExtension = path.extname(file.originalname).toLowerCase().substring(1);
        if (allowedTypes.includes(fileExtension)) {
            cb(null, true);
        } else {
            cb(new Error('File type not supported'), false);
        }
    } catch (err) {
        cb(new Error('File type not supported'), false);
    }
};

const upload = multer({
    storage: storage,
    fileFilter: fileFilter
});

// const notificationQueue = require('../queues/notificationQueue');
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'; // Default to localhost if not set in .env

class EventController {
    // Add a new event
    async addEvent(req, res) {
        try {
            const { event_title, event_date, event_description } = req.body;
            const event_photo = req.file ? req.file.filename : '';

            const connection = await db.getConnection();

            try {
                const query = `INSERT INTO events (vEventTitle, dEventDate, tEventDescription, vEventPhoto, dAddedDate, dModifiedDate, iIsDeleted)
                              VALUES (?, ?, ?, ?, NOW(), NOW(), 0)`;

                const [results] = await connection.execute(query, [event_title, event_date, event_description, event_photo]);
                connection.release();

                // // Enqueue the notification job
                // const notificationPayload = {
                //     notification: {
                //         title: 'New Update',
                //         body: `${event_title}`,
                //     },
                //     data: {
                //         event_id: results.insertId.toString(),
                //         event_title,
                //         event_date: moment(event_date).format('YYYY-MM-DD'),
                //         event_description,
                //     },
                // };

                // notificationQueue.add({ notificationPayload });

                res.status(200).json({
                    status: true,
                    message: 'Event added successfully',
                    data: { event_id: results.insertId }
                });
            } catch (queryErr) {
                connection.release();

                // Clean up uploaded file if query fails
                if (event_photo) {
                    fs.unlink(path.join('uploads/events/', event_photo), () => {});
                }

                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            // Clean up uploaded file if connection fails
            const event_photo = req.file ? req.file.filename : '';
            if (event_photo) {
                fs.unlink(path.join('uploads/events/', event_photo), () => {});
            }

            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Update an event
    async updateEvent(req, res) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ status: false, message: 'Validation errors', errors: errors.array() });
        }

        const { event_id, event_title, event_date, event_description } = req.body;
        const event_photo = req.file ? req.file.filename : '';

        try {
            const connection = await db.getConnection();

            try {
                // Check if event exists
                const [existingResults] = await connection.execute('SELECT vEventPhoto FROM events WHERE iEventsId = ?', [event_id]);

                if (existingResults.length === 0) {
                    connection.release();
                    return res.status(404).json({ status: false, message: 'Event not found' });
                }

                const oldPhoto = existingResults[0].vEventPhoto;

                // If a new photo is uploaded, delete the old one
                if (event_photo && oldPhoto) {
                    fs.unlink(path.join('uploads/events/', oldPhoto), () => {});
                }

                // Update event
                const query = `
                    UPDATE events
                    SET vEventTitle = ?, dEventDate = ?, tEventDescription = ?, vEventPhoto = ?, dModifiedDate = NOW()
                    WHERE iEventsId = ?
                `;
                const values = [event_title, event_date, event_description, event_photo || oldPhoto, event_id];

                await connection.query(query, values);
                connection.release();

                res.status(200).json({
                    status: true,
                    message: 'Event updated successfully'
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Delete an event
    async deleteEvent(req, res) {
        const { event_id } = req.body;

        try {
            const connection = await db.getConnection();

            try {
                // Check if event exists
                const [existingResults] = await connection.execute('SELECT vEventPhoto FROM events WHERE iEventsId = ?', [event_id]);

                if (existingResults.length === 0) {
                    connection.release();
                    return res.status(404).json({ status: false, message: 'Event not found' });
                }

                const eventPhoto = existingResults[0].vEventPhoto;

                // Delete event
                await connection.execute('DELETE FROM events WHERE iEventsId = ?', [event_id]);
                connection.release();

                // If event photo exists, delete the file
                if (eventPhoto) {
                    fs.unlink(path.join('uploads/events/', eventPhoto), () => {});
                }

                res.status(200).json({
                    status: true,
                    message: 'Event deleted successfully'
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }

    // Get events list with pagination
    async getEvents(req, res) {
        const perPage = 20;
        const page = parseInt(req.query.page) || 1;
        const search = req.query.search || '';
        const offset = (page - 1) * perPage;
        let searchQuery = '';
        let searchValues = [];

        if (search) {
            searchQuery = ' AND (vEventTitle LIKE ? OR tEventDescription LIKE ?)';
            searchValues = [`%${search}%`, `%${search}%`];
        }

        try {
            const connection = await db.getConnection();

            try {
                const countQuery = `SELECT COUNT(*) AS total FROM events WHERE iIsDeleted = 0${searchQuery}`;
                const eventsQuery = `SELECT * FROM events WHERE iIsDeleted = 0${searchQuery} ORDER BY dEventDate DESC, iEventsId DESC LIMIT ? OFFSET ?`;

                // Execute both queries in parallel
                const [
                    [countResults],
                    [eventsResults]
                ] = await Promise.all([
                    connection.query(countQuery, searchValues),
                    connection.query(eventsQuery, [...searchValues, perPage, offset])
                ]);

                connection.release();

                const totalItems = countResults[0].total;
                const totalPages = Math.ceil(totalItems / perPage);

                if (totalItems === 0) {
                    return res.status(404).json({ status: false, message: 'No events found.' });
                }

                const events = eventsResults.map(event => ({
                    event_id: event.iEventsId,
                    event_title: event.vEventTitle,
                    event_date: moment(event.dEventDate).format('YYYY-MM-DD'),
                    event_description: event.tEventDescription,
                    event_photo: event.vEventPhoto ? `${BASE_URL}/uploads/events/${event.vEventPhoto}` : ''
                }));

                res.status(200).json({
                    status: true,
                    message: 'Events found.',
                    data: {
                        current_page: page,
                        per_page: perPage,
                        total_pages: totalPages,
                        total_items: totalItems,
                        events: events
                    }
                });
            } catch (queryErr) {
                connection.release();
                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = {
    EventController,
    upload // Export the upload middleware
};
