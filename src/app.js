const express = require('express');
const cors = require('cors');
const setRoutes = require('./routes/index');
const app = express();

// Initialize database connection asynchronously (non-blocking)
setTimeout(() => {
    try {
        require('./db'); // This will start the database connection process
        console.log('🔄 Database connection initialization started in background...');
    } catch (err) {
        console.error('❌ Failed to initialize database module:', err.message);
    }
}, 100);

// Add a simple health check route
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        message: 'Server is running',
        timestamp: new Date().toISOString()
    });
});

// CORS configuration
const corsOptions = {
    origin: (origin, callback) => {
        const allowedOrigins = [
            'https://admin.dabgarsamaj.in',
            'https://api.dabgarsamaj.in',
            'https://dabgarsamaj.in'
        ];

        console.log('CORS Origin Check:', {
            origin: origin,
            nodeEnv: process.env.NODE_ENV,
            allowedOrigins: allowedOrigins
        });

        // In production, validate origin and allow requests from allowed origins
        if (process.env.NODE_ENV === 'production') {
            // Allow requests with no origin (like Postman) or from allowed origins
            if (!origin || allowedOrigins.includes(origin)) {
                callback(null, true);
            } else {
                console.error('CORS blocked origin:', origin);
                callback(new Error('Not allowed by CORS'));
            }
        } else {
            // In development, allow all origins
            callback(null, true);
        }
    },
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'adminid', 'token', 'Origin', 'Accept', 'X-Requested-With'],
    exposedHeaders: ['Content-Length', 'Date'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
    maxAge: 86400 // 24 hours
};

// Handle CORS pre-flight for all routes
app.use(cors(corsOptions));

// Enable pre-flight requests for all routes
app.options('*', cors(corsOptions));

// Additional explicit CORS handling for preflight requests
app.use((req, res, next) => {
    if (req.method === 'OPTIONS') {
        console.log('Handling OPTIONS preflight request:', {
            origin: req.headers.origin,
            method: req.method,
            path: req.path,
            headers: req.headers
        });

        // Set CORS headers explicitly for preflight
        const origin = req.headers.origin;
        const allowedOrigins = [
            'https://admin.dabgarsamaj.in',
            'https://api.dabgarsamaj.in',
            'https://dabgarsamaj.in'
        ];

        if (process.env.NODE_ENV === 'production') {
            if (!origin || allowedOrigins.includes(origin)) {
                res.header('Access-Control-Allow-Origin', origin || '*');
            }
        } else {
            res.header('Access-Control-Allow-Origin', origin || '*');
        }

        res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,adminid,token,Origin,Accept,X-Requested-With');
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Access-Control-Max-Age', '86400');

        return res.status(204).end();
    }
    next();
});

// Handle CORS errors immediately after CORS middleware
app.use((err, req, res, next) => {
    if (err && err.message === 'Not allowed by CORS') {
        console.error('CORS Error:', {
            origin: req.headers.origin,
            method: req.method,
            path: req.path
        });
        return res.status(403).json({
            status: false,
            message: 'CORS not allowed for this origin'
        });
    }
    next(err);
});

// Add security headers
app.use((req, res, next) => {
    res.header('X-Content-Type-Options', 'nosniff');
    res.header('X-Frame-Options', 'DENY');
    res.header('X-XSS-Protection', '1; mode=block');
    res.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    next();
});

// Regular middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Set up routes
setRoutes(app);

// Export the app
module.exports = app;