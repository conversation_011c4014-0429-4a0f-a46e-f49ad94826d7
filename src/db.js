const mysql = require('mysql2/promise');
require('dotenv').config();

// Validate required environment variables
const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_NAME'];
if (process.env.NODE_ENV === 'production') {
    requiredEnvVars.push('DB_PASSWORD');
}

for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
        console.error(`Missing required environment variable: ${envVar}`);
        process.exit(1);
    }
}

// Database configuration
const dbConfig = {
    connectionLimit: 5, // Reduce connection pool size
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    queueLimit: 0,
    // Connection timeouts
    connectTimeout: 30000,
    acquireTimeout: 30000,
    timeout: 30000,
    // SSL Configuration
    ssl: process.env.DB_SSL === 'false' ? false : {
        rejectUnauthorized: false
    },
    charset: 'utf8mb4',
    timezone: '+00:00',
    // IMPORTANT: Disable prepared statements to avoid the limit
    namedPlaceholders: false,
    // Connection management
    idleTimeout: 300000, // 5 minutes
    maxIdle: 2,
    // Prevent prepared statement buildup
    supportBigNumbers: true,
    bigNumberStrings: true
};

console.log('Database config:', {
    host: dbConfig.host,
    user: dbConfig.user,
    database: dbConfig.database,
    port: dbConfig.port,
    env: process.env.NODE_ENV
});

let pool = null;
let isConnected = false;
let retryCount = 0;
const maxRetries = 5;
const initialRetryInterval = 1000;

function getRetryTimeout() {
    return Math.min(initialRetryInterval * Math.pow(2, retryCount), 30000);
}

async function createPool() {
    try {
        console.log('Attempting to create database pool with config:', {
            host: dbConfig.host,
            user: dbConfig.user,
            database: dbConfig.database,
            port: dbConfig.port,
            ssl: dbConfig.ssl ? 'enabled' : 'disabled'
        });

        // Create the pool with promise interface
        const newPool = mysql.createPool(dbConfig);

        // Test the connection with timeout
        const conn = await Promise.race([
            newPool.getConnection(),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Connection timeout after 30 seconds')), 30000)
            )
        ]);

        console.log('Connection acquired, testing with ping...');
        await conn.ping();

        // Test a simple query
        const [rows] = await conn.execute('SELECT 1 as test');
        console.log('Database query test successful:', rows);

        conn.release();
        console.log('Pool created and connection tested successfully');
        return newPool;
    } catch (err) {
        console.error('Failed to create pool:', {
            message: err.message,
            code: err.code,
            errno: err.errno,
            sqlState: err.sqlState,
            sqlMessage: err.sqlMessage
        });
        throw err;
    }
}

async function testConnectionWithConfig(config, configName) {
    let testPool = null;
    try {
        console.log(`Testing connection with ${configName}...`);
        
        // Create a minimal test connection instead of pool
        const testConnection = await mysql.createConnection({
            host: config.host,
            user: config.user,
            password: config.password,
            database: config.database,
            port: config.port,
            connectTimeout: 15000,
            ssl: config.ssl || false
        });

        // Simple ping test
        await testConnection.ping();
        
        // Simple query without prepared statements
        const [rows] = await testConnection.query('SELECT 1 as test');
        
        await testConnection.end();
        
        console.log(`✅ ${configName} connection successful:`, rows[0]);
        return config;
        
    } catch (err) {
        console.log(`❌ ${configName} connection failed:`, err.message);
        if (testPool) {
            try {
                await testPool.end();
            } catch (e) {
                // Ignore cleanup errors
            }
        }
        throw err;
    }
}

async function testConnection() {
    try {
        if (!pool) {
            console.log('Testing database connection...');

            // Simple configuration without prepared statements
            const simpleConfig = {
                host: process.env.DB_HOST,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME,
                port: process.env.DB_PORT || 3306,
                connectTimeout: 30000,
                ssl: false,
                namedPlaceholders: false
            };

            try {
                await testConnectionWithConfig(simpleConfig, 'Simple Config');
                console.log('🎉 Found working configuration: Simple Config');
                pool = mysql.createPool(simpleConfig);
            } catch (err) {
                throw new Error('Database connection failed: ' + err.message);
            }
        }

        console.log('Testing established pool connection...');
        const conn = await pool.getConnection();
        
        // Use simple query instead of execute (which uses prepared statements)
        const [rows] = await conn.query('SELECT 1 as test, DATABASE() as db_name');
        conn.release();

        isConnected = true;
        retryCount = 0;
        console.log('✅ Database connection successful:', rows[0]);
        return true;
        
    } catch (err) {
        isConnected = false;
        retryCount++;

        console.error('❌ Connection error:', err.message);

        if (err.fatal && pool) {
            try {
                await pool.end();
            } catch (e) {}
            pool = null;
        }

        if (retryCount >= maxRetries) {
            console.error('Max retries reached. Database will remain disconnected.');
            return false;
        }

        const timeout = getRetryTimeout();
        console.log(`Retrying in ${timeout/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, timeout));
        return testConnection();
    }
}

// Initialize pool and test connection in background
setTimeout(async () => {
    try {
        console.log('🔄 Starting database connection initialization...');
        const success = await testConnection();
        if (success) {
            console.log('🎉 Database connection established successfully!');
        } else {
            console.log('⚠️  Database connection failed. Server will continue without database.');
            console.log('📋 To fix this issue:');
            console.log('   1. Add your IP address (************) to the database whitelist');
            console.log('   2. Or contact your hosting provider to allow remote connections');
            console.log('   3. Check database user permissions');
        }
    } catch (err) {
        console.error('❌ Initial connection setup failed:', err.message);
        console.log('📋 Server will continue running without database connection.');
    }
}, 1000); // Start database connection attempt after a short delay

// Add graceful shutdown
process.on('SIGTERM', async () => {
    console.log('Shutting down database connections...');
    if (pool) {
        try {
            await pool.end();
            console.log('Database pool closed.');
        } catch (err) {
            console.error('Error closing database pool:', err.message);
        }
    }
});

process.on('SIGINT', async () => {
    console.log('Shutting down database connections...');
    if (pool) {
        try {
            await pool.end();
            console.log('Database pool closed.');
        } catch (err) {
            console.error('Error closing database pool:', err.message);
        }
    }
    process.exit(0);
});

// Export a proxy object that ensures pool exists before use
const proxyHandler = {
    get(target, prop) {
        if (!pool) {
            if (prop === 'isConnected') {
                return () => false;
            }
            throw new Error('Database connection not initialized');
        }
        if (prop === 'isConnected') {
            return () => isConnected;
        }
        return pool[prop];
    }
};

module.exports = new Proxy({}, proxyHandler);
