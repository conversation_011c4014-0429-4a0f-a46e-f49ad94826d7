const { body } = require('express-validator');

const stateValidationRules = () => {
    return [
        body('state_name').matches(/^[A-Za-z\s]+$/).withMessage('State name must contain only alphabets and spaces'),
        body('state_code').isAlpha().withMessage('State code must contain only alphabets'),
        body('status').isAlpha().withMessage('Status must contain only alphabets')
    ];
};

module.exports = {
    stateValidationRules
};