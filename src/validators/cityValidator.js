const { body } = require('express-validator');

const cityValidationRules = () => {
    return [
        body('state_id').isInt().withMessage('State ID must be an integer'),
        body('city_name').matches(/^[A-Za-z\s]+$/).withMessage('City name must contain only alphabets and spaces'),
        body('city_code').isAlpha().withMessage('City code must contain only alphabets'),
        body('status').isAlpha().withMessage('Status must contain only alphabets')
    ];
};

module.exports = {
    cityValidationRules
};