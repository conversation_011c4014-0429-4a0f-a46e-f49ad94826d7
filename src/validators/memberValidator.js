const { body } = require('express-validator');

const memberValidationRules = () => {
    return [
        body('member_name').matches(/^[A-Za-z\s]+$/).withMessage('Member name must contain only alphabets and spaces'),
        body('mobile_number').optional({ checkFalsy: true }).isMobilePhone().withMessage('Invalid mobile number'),
        body('email').optional({ checkFalsy: true }).isEmail().withMessage('Invalid email address'),
        body('pincode').isPostalCode('any').withMessage('Invalid pincode'),
        body('dob').optional({ checkFalsy: true }).isISO8601().withMessage('Invalid date of birth'),  // Ensure the date is in ISO 8601 format
        body('martial_status').isIn(['Single','Married','Divorced','Widowed','Engaged','Vat Kari <PERSON>e']).withMessage('Invalid marital status'),
        body('status').isIn(['Active','Inactive','Death','NaatBahar']).withMessage('Invalid marital status'),
        body('gender').isIn(['Male', 'Female']).withMessage('Invalid gender'),
        body('state_id').notEmpty().withMessage('State is required'),
        body('city_id').notEmpty().withMessage('City is required'),
    ];
};

module.exports = {
    memberValidationRules
};