const db = require('../db');
const moment = require('moment');

class DsMemberController {
    // Get members list with pagination (no token validation)
    async getMembers(req, res) {
        let connection;
        try {
            connection = await db.getConnection();

            const perPage = parseInt(req.query.per_page) || 20; // Default items per page
            const page = parseInt(req.query.page) || 1; // Default to page 1
            const search = req.query.search || ''; // Search query
            const offset = (page - 1) * perPage; // Calculate offset for pagination
            let searchQuery = '';
            let searchValues = [];

            const { city_id, martial_status, gender, min_age, max_age, status, is_physically_challenged } = req.query;

            if (city_id) {
                searchQuery += ' AND member.iCityId = ?';
                searchValues.push(city_id);
            }

            if (martial_status) {
                searchQuery += ' AND member.eMartialStatus = ?';
                searchValues.push(martial_status);
            }

            if (gender) {
                searchQuery += ' AND member.eGender = ?';
                searchValues.push(gender);
            }

            if ((min_age && min_age !== 'null') || (max_age && max_age !== 'null')) {
                searchQuery += ' AND (YEAR(CURDATE()) - YEAR(member.dDob)) BETWEEN ? AND ?';
                searchValues.push(min_age || 0, max_age || 100);
            }

            if (status) {
                searchQuery += ' AND member.eStatus = ?';
                searchValues.push(status);
            }

            if (is_physically_challenged) {
                searchQuery += ' AND member.iIsPhysicallyChallenged = ?';
                searchValues.push(is_physically_challenged);
            }

            // Add search condition if search query is provided
            if (search) {
                searchQuery += ` AND (member.vMemberName LIKE ? OR member.vNickName LIKE ? OR member.vMemberNameGj LIKE ? OR member.vNickNameGj LIKE ? OR member.vMemberNameHi LIKE ? OR member.vNickNameHi LIKE ?)`;
                searchValues.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
            }

            // Query to get total count of members
            const countQuery = `SELECT COUNT(*) AS total FROM member WHERE iIsDeleted = 0${searchQuery}`;

            // Query to get paginated members
            const membersQuery = `
                SELECT member.*, 
                       state.vStateName, 
                       city.vCityName,
                       father.vMemberName AS father_name,
                       father.vMemberNameGj AS father_name_gj,
                       father.vMemberNameHi AS father_name_hi,
                       mother.vMemberName AS mother_name,
                       mother.vMemberNameGj AS mother_name_gj,
                       mother.vMemberNameHi AS mother_name_hi,
                       spouse.vMemberName AS spouse_name,
                       spouse.vMemberNameGj AS spouse_name_gj,
                       spouse.vMemberNameHi AS spouse_name_hi
                FROM member
                LEFT JOIN state ON member.iStateId = state.iStateId
                LEFT JOIN city ON member.iCityId = city.iCityId
                LEFT JOIN member AS father ON member.iFatherId = father.iMemberId
                LEFT JOIN member AS mother ON member.iMotherId = mother.iMemberId
                LEFT JOIN member AS spouse ON member.iSpouseId = spouse.iMemberId
                WHERE member.iIsDeleted = 0${searchQuery}
                ORDER BY member.iMemberId DESC
                LIMIT ? OFFSET ?
            `;

            const [
                [countResults],
                [membersResults]
            ] = await Promise.all([
                connection.query(countQuery, searchValues),
                connection.query(membersQuery, [...searchValues, perPage, offset])
            ]);

            const countResult = countResults[0];
            const membersResult = membersResults;

            const totalItems = countResult.total;
            const totalPages = Math.ceil(totalItems / perPage);

            // If no members are found, return a 404 response
            if (totalItems === 0) {
                return res.status(404).json({ status: false, message: 'No members found.' });
            }

            // Map members to the desired format
            const members = membersResult.map(member => ({
                member_id: member.iMemberId,
                member_code: member.vMemberCode,
                member_name: member.vMemberName,
                member_name_gj: member.vMemberNameGj,
                member_name_hi: member.vMemberNameHi,
                nick_name: member.vNickName,
                nick_name_gj: member.vNickNameGj,
                nick_name_hi: member.vNickNameHi,
                mobile_number: member.vMobileNumber,
                alternate_number: member.vAlternateMobileNumber,
                father_id: member.iFatherId,
                father_name: member.father_name,
                father_name_gj: member.father_name_gj,
                father_name_hi: member.father_name_hi,
                mother_id: member.iMotherId,
                mother_name: member.mother_name,
                mother_name_gj: member.mother_name_gj,
                mother_name_hi: member.mother_name_hi,
                spouse_id: member.iSpouseId,
                spouse_name: member.spouse_name,
                spouse_name_gj: member.spouse_name_gj,
                spouse_name_hi: member.spouse_name_hi,
                gender: member.eGender,
                address: member.tAddress,
                state_id: member.iStateId,
                state: member.vStateName,
                city_id: member.iCityId,
                city: member.vCityName,
                pincode: member.vPincode,
                panch_city: member.vPanchCity,
                email: member.vEmail,
                dob: member.dDob ? moment(member.dDob).format('YYYY-MM-DD') : null, // Format the date of birth
                age: member.dDob ? moment().diff(moment(member.dDob), 'years') : null, // Calculate age
                martial_status: member.eMartialStatus,
                graduation: member.vGraduation,
                school_collage_name: member.vSchoolCollegeName,
                hsc: member.vHSC,
                ssc: member.vSSC,
                occupation: member.vOccupation,
                company_ferm_name: member.vCompanyFermName,
                experience: member.vExperience,
                other_business: member.vOtherBusiness,
                physically_challenged: member.iIsPhysicallyChallenged,
                physically_challenged_details: member.vPhysicallyChallengedDetails,
                member_status: member.eStatus
            }));

            // Send response
            res.status(200).json({
                status: true,
                message: 'Members found successfully.',
                data: {
                    current_page: page,
                    per_page: perPage,
                    total_pages: totalPages,
                    total_items: totalItems,
                    members
                }
            });
        } catch (error) {
            if (connection) connection.release();

            res.status(500).json({
                status: false,
                message: 'Database error occurred',
                error: error.message
            });
        }
    }

    // Helper function to fetch children
    fetchChildren = async (memberId, existingConnection = null) => {
        const shouldReleaseConnection = !existingConnection;
        const connection = existingConnection || await db.getConnection();

        try {
            const query = `
                SELECT iMemberId, vMemberName, vMemberNameGj, vMemberNameHi, (YEAR(CURDATE()) - YEAR(dDob)) AS age, eGender
                FROM member
                WHERE (iFatherId = ? OR iMotherId = ?) AND iIsDeleted = 0
            `;
            const [results] = await connection.execute(query, [memberId, memberId]);

            // Recursively fetch the family tree for each child
            const childrenWithFamily = await Promise.all(
                results.map(async (child) => ({
                    id: child.iMemberId,
                    name: child.vMemberName,
                    name_gj: child.vMemberNameGj,
                    name_hi: child.vMemberNameHi,
                    age: child.age,
                    gender: child.eGender,
                    spouse: await this.fetchSpouse(child.iMemberId, connection),
                    childrens: await this.fetchChildren(child.iMemberId, connection) // Recursive call
                }))
            );

            if (shouldReleaseConnection) connection.release();
            return childrenWithFamily;
        } catch (error) {
            if (shouldReleaseConnection && connection) connection.release();
            throw error;
        }
    };

    // Helper function to fetch spouse details
    fetchSpouse = async (memberId, existingConnection = null) => {
        const shouldReleaseConnection = !existingConnection;
        const connection = existingConnection || await db.getConnection();

        try {
            const query = `
                SELECT member.iSpouseId, spouse.vMemberName AS spouse_name, spouse.vMemberNameGj AS spouse_name_gj, spouse.vMemberNameHi AS spouse_name_hi, spouse.eGender AS spouse_gender, spouse.dDob AS spouse_dob
                FROM member
                LEFT JOIN member AS spouse ON member.iSpouseId = spouse.iMemberId
                WHERE member.iMemberId = ? AND member.iIsDeleted = 0
            `;
            const [results] = await connection.execute(query, [memberId]);

            if (shouldReleaseConnection) connection.release();

            if (results.length === 0 || !results[0].iSpouseId) {
                return null; // No spouse found
            }

            const spouse = results[0];
            return {
                spouse_id: spouse.iSpouseId,
                spouse_name: spouse.spouse_name,
                spouse_name_gj: spouse.spouse_name_gj,
                spouse_name_hi: spouse.spouse_name_hi,
                age: moment().diff(moment(spouse.spouse_dob), 'years'),
                gender: spouse.spouse_gender
            };
        } catch (error) {
            if (shouldReleaseConnection && connection) connection.release();
            throw error;
        }
    };

    // Recursive function to build the family tree
    fetchFamilyTree = async (memberId) => {
        let connection;
        try {
            connection = await db.getConnection();

            const query = `
                SELECT member.iMemberId, member.vMemberName, member.vMemberNameGj, member.vMemberNameHi, member.vNickName, member.vNickNameGj, member.vNickNameHi,
                       member.eGender, member.dDob, member.eMartialStatus, member.iFatherId, member.iMotherId, member.iSpouseId,
                       father.vMemberName AS father_name, father.vMemberNameGj AS father_name_gj, father.vMemberNameHi AS father_name_hi, father.eGender AS father_gender, father.dDob AS father_dob,
                       mother.vMemberName AS mother_name, mother.vMemberNameGj AS mother_name_gj, mother.vMemberNameHi AS mother_name_hi, mother.eGender AS mother_gender, mother.dDob AS mother_dob,
                       spouse.vMemberName AS spouse_name, spouse.vMemberNameGj AS spouse_name_gj, spouse.vMemberNameHi AS spouse_name_hi, spouse.eGender AS spouse_gender, spouse.dDob AS spouse_dob
                FROM member
                LEFT JOIN member AS father ON member.iFatherId = father.iMemberId
                LEFT JOIN member AS mother ON member.iMotherId = mother.iMemberId
                LEFT JOIN member AS spouse ON member.iSpouseId = spouse.iMemberId
                WHERE member.iMemberId = ? AND member.iIsDeleted = 0
            `;

            const [results] = await connection.execute(query, [memberId]);

            if (results.length === 0) {
                if (connection) connection.release();
                return null; // No member found
            }

            const member = results[0];

            // Build the family tree for the current member
            const familyTreeData = {
                member_id: member.iMemberId,
                member_name: member.vMemberName,
                member_name_gj: member.vMemberNameGj,
                member_name_hi: member.vMemberNameHi,
                nick_name: member.vNickName,
                nick_name_gj: member.vNickNameGj,
                nick_name_hi: member.vNickNameHi,
                gender: member.eGender,
                dob: member.dDob ? moment(member.dDob).format('YYYY-MM-DD') : null,
                age: member.dDob ? moment().diff(moment(member.dDob), 'years') : null,
                martial_status: member.eMartialStatus,
                spouse: member.iSpouseId
                    ? {
                          spouse_id: member.iSpouseId,
                          spouse_name: member.spouse_name,
                          spouse_name_gj: member.spouse_name_gj,
                          spouse_name_hi: member.spouse_name_hi,
                          age: moment().diff(moment(member.spouse_dob), 'years'),
                          gender: member.spouse_gender
                      }
                    : null,
                family_tree: {
                    father: member.iFatherId
                    ? {
                          father_id: member.iFatherId,
                          father_name: member.father_name,
                            father_name_gj: member.father_name_gj,
                            father_name_hi: member.father_name_hi,
                          age: moment().diff(moment(member.father_dob), 'years'),
                          gender: member.father_gender
                      }
                    : null,
                    mother: member.iMotherId
                    ? {
                          mother_id: member.iMotherId,
                          mother_name: member.mother_name,
                            mother_name_gj: member.mother_name_gj,
                            mother_name_hi: member.mother_name_hi,
                          age: moment().diff(moment(member.mother_dob), 'years'),
                          gender: member.mother_gender
                      }
                    : null,
                    childrens: await this.fetchChildren(member.iFatherId || member.iMotherId || member.iMemberId, connection) // Fetch children for the current member
                },
                 // Correctly assign the array of children
            };

            if (connection) connection.release();
            return familyTreeData;
        } catch (error) {
            if (connection) connection.release();
            console.error('Family tree error:', error);
            throw error;
        }
    };

    // Get N-level family tree for a member
    getFamilyTree = async (req, res) => {
        const { member_id } = req.query;

        if (!member_id) {
            return res.status(400).json({ status: false, message: 'Member ID is required.' });
        }

        try {
            const familyTree = await this.fetchFamilyTree(member_id);

            if (!familyTree) {
                return res.status(404).json({ status: false, message: 'Member not found.' });
            }

            res.status(200).json({
                status: true,
                message: 'Family tree fetched successfully.',
                data: { member_data: familyTree }
            });
        } catch (error) {
            res.status(500).json({
                status: false,
                message: 'Failed to fetch family tree.',
                error: error.message
            });
        }
    };
}

module.exports = DsMemberController;
