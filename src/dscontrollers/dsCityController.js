const db = require('../db');

class DsCityController {
    // Get Active Cities
    async getActiveCities(req, res) {
        try {
            const connection = await db.getConnection();

            try {
                const query = `
                    SELECT city.iCityId, city.iStateId, city.vCityName, city.vCityCode, state.vStateName
                    FROM city
                    JOIN state ON city.iStateId = state.iStateId
                    WHERE city.iIsDeleted = 0 AND city.eStatus = 'Active'
                `;

                const [results] = await connection.query(query);
                connection.release();

                if (results.length === 0) {
                    return res.status(200).json({ status: false, message: 'No active cities found.' });
                }

                const cities = results.map(city => ({
                    city_id: city.iCityId,
                    state_id: city.iStateId,
                    state_name: city.vStateName,
                    city_name: city.vCityName,
                    city_code: city.vCityCode
                }));

                res.status(200).json({
                    status: true,
                    message: 'Active cities found successfully',
                    data: { cities }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = DsCityController;
