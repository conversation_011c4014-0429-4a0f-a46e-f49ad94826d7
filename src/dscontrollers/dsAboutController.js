const db = require('../db');

class DsAboutController {
    // Get about information without token validation
    async getAbout(req, res) {
        try {
            const connection = await db.getConnection();

            try {
                const [results] = await connection.query('SELECT * FROM about LIMIT 1');
                connection.release();

                if (results.length === 0) {
                    return res.status(404).json({ status: false, message: 'No about information found.' });
                }

                const about = results[0];

                res.status(200).json({
                    status: true,
                    message: 'About information found successfully',
                    data: {
                        id: about.iAboutId,
                        title: about.vTitle,
                        description: about.tDescription
                    }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = DsAboutController;
