const db = require('../db');

class DsRulesController {
    // Get rules without token validation
    async getRules(req, res) {
        try {
            const connection = await db.getConnection();

            try {
                const [results] = await connection.query('SELECT * FROM rules LIMIT 1');
                connection.release();

                if (results.length === 0) {
                    return res.status(404).json({ status: false, message: 'No rules found.' });
                }

                const rule = results[0];

                res.status(200).json({
                    status: true,
                    message: 'Rules found successfully',
                    data: {
                        id: rule.iRulesId,
                        title: rule.vTitle,
                        description: rule.tDescription
                    }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = DsRulesController;
