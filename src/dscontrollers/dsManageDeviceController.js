const db = require('../db');

class DsManageDeviceController {
    async addOrUpdateDevice(req, res) {
        try {
            const { device_id, device_token, device_type } = req.body;

            if (!device_id || !device_token || !device_type) {
                return res.status(400).json({ status: false, message: 'Missing required fields.' });
            }

            if (!['android', 'ios'].includes(device_type.toLowerCase())) {
                return res.status(400).json({ status: false, message: 'Invalid device type.' });
            }

            const connection = await db.getConnection();

            try {
                // Check if device exists
                const [existingResults] = await connection.query(
                    'SELECT vDeviceID FROM device_tokens WHERE vDeviceID = ?',
                    [device_id]
                );

                const now = new Date();
                const isUpdate = existingResults.length > 0;

                let query, values;
                if (isUpdate) {
                    query = 'UPDATE device_tokens SET tDeviceToken = ?, eDeviceType = ?, dModifiedDate = ? WHERE vDeviceID = ?';
                    values = [device_token, device_type, now, device_id];
                } else {
                    query = 'INSERT INTO device_tokens (vDeviceID, tDeviceToken, eDeviceType, dAddedDate, dModifiedDate) VALUES (?, ?, ?, ?, ?)';
                    values = [device_id, device_token, device_type, now, now];
                }

                const [results] = await connection.query(query, values);
                connection.release();

                return res.status(200).json({
                    status: true,
                    message: isUpdate ? 'Device updated successfully' : 'Device added successfully'
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error'
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = DsManageDeviceController;
