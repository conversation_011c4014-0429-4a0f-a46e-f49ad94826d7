const db = require('../db');
const moment = require('moment');
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'; // Default to localhost if not set in .env

class DsEventController {
    // Get events list with pagination and search (no token validation)
    async getEvents(req, res) {
        try {
            const perPage = parseInt(req.query.per_page) || 20; // Default items per page
            const page = parseInt(req.query.page) || 1; // Default to page 1
            const search = req.query.search || ''; // Search query
            const offset = (page - 1) * perPage; // Calculate offset for pagination
            let searchQuery = '';
            let searchValues = [];

            // Add search condition if search query is provided
            if (search) {
                searchQuery = ' AND (vEventTitle LIKE ? OR tEventDescription LIKE ?)';
                searchValues = [`%${search}%`, `%${search}%`];
            }

            const connection = await db.getConnection();

            try {
                // Queries for total count and paginated events
                const countQuery = `SELECT COUNT(*) AS total FROM events WHERE iIsDeleted = 0${searchQuery}`;
                const eventsQuery = `SELECT * FROM events WHERE iIsDeleted = 0${searchQuery} ORDER BY dEventDate DESC, iEventsId DESC LIMIT ? OFFSET ?`;

                // Execute both queries in parallel
                const [
                    [countResults],
                    [eventsResults]
                ] = await Promise.all([
                    connection.query(countQuery, searchValues),
                    connection.query(eventsQuery, [...searchValues, perPage, offset])
                ]);

                connection.release();

                const totalItems = countResults[0].total;
                const totalPages = Math.ceil(totalItems / perPage);

                // If no events are found, return a 404 response
                if (totalItems === 0) {
                    return res.status(404).json({ status: false, message: 'No events found.' });
                }

                // Map events to the desired format
                const events = eventsResults.map(event => ({
                    event_id: event.iEventsId,
                    event_title: event.vEventTitle,
                    event_date: moment(event.dEventDate).format('YYYY-MM-DD'),
                    event_description: event.tEventDescription,
                    event_photo: event.vEventPhoto ? `${BASE_URL}/uploads/events/${event.vEventPhoto}` : ''
                }));

                // Send response
                res.status(200).json({
                    status: true,
                    message: 'Events found.',
                    data: {
                        current_page: page,
                        per_page: perPage,
                        total_pages: totalPages,
                        total_items: totalItems,
                        events: events
                    }
                });
            } catch (queryErr) {
                connection.release();

                return res.status(500).json({
                    status: false,
                    message: 'Database error',
                    error: queryErr.message
                });
            }
        } catch (err) {
            return res.status(500).json({
                status: false,
                message: 'Database connection error'
            });
        }
    }
}

module.exports = DsEventController;
