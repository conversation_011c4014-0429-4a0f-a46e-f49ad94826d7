const express = require('express');
const { body } = require('express-validator');
const AdminController = require('../controllers/adminController');
const StateController = require('../controllers/stateController'); // Update import if needed
const CityController = require('../controllers/cityController');
const MemberController = require('../controllers/memberController');
const { EventController, upload } = require('../controllers/eventController');
const RulesController = require('../controllers/rulesController');
const AboutController = require('../controllers/aboutController');
const validateToken = require('../middleware/auth');
const validate = require('../middleware/validate');
const sanitize = require('../middleware/sanitize');
const checkDbConnection = require('../middleware/dbCheck');
const { cityValidationRules } = require('../validators/cityValidator');
const { stateValidationRules } = require('../validators/stateValidator');
const { memberValidationRules } = require('../validators/memberValidator');
const { eventValidationRules } = require('../validators/eventValidator');
const DashboardController = require('../controllers/dashboardController');
const dashboardController = new DashboardController();
const adminController = new AdminController();
const stateController = new StateController();
const cityController = new CityController();
const memberController = new MemberController();
const eventController = new EventController();
const rulesController = new RulesController();
const aboutController = new AboutController();

// For Ds Apis
const DsCityController = require('../dscontrollers/dsCityController');
const dsCityController = new DsCityController();
const DsDashboardController = require('../dscontrollers/dsDashboardController');
const dsDashboardController = new DsDashboardController();
const DsRulesController = require('../dscontrollers/dsRulesController');
const dsRulesController = new DsRulesController();
const DsAboutController = require('../dscontrollers/dsAboutController');
const dsAboutController = new DsAboutController();
const DsEventController = require('../dscontrollers/dsEventController');
const dsEventController = new DsEventController();
const DsMemberController = require('../dscontrollers/dsMemberController');
const dsMemberController = new DsMemberController();
const DsManageDeviceController = require('../dscontrollers/dsManageDeviceController');
const dsManageDeviceController = new DsManageDeviceController();

function setRoutes(app) {

    // Serve static files from the 'uploads/events' directory
    app.use('/uploads/events', express.static('uploads/events'));

    // Authentication routes
    app.post('/api/admin/login', checkDbConnection, adminController.login);

    // Dashboard route
    app.get('/api/admin/dashboard', checkDbConnection, validateToken, dashboardController.getDashboardDetails);

    // State Routes
    app.get('/api/admin/states', checkDbConnection, validateToken, stateController.getStateList);
    app.post('/api/admin/state', [
        validateToken,
        ...stateValidationRules(),
        sanitize(),
        validate
    ], stateController.addState);
    app.put('/api/admin/state', [
        validateToken,
        body('state_id').isInt().withMessage('State ID must be an integer'),
        ...stateValidationRules(),
        sanitize(),
        validate
    ], stateController.updateState);
    app.delete('/api/admin/state/delete', [
        validateToken,
        body('state_id').isInt().withMessage('State ID must be an integer'),
        sanitize(),
        validate
    ], stateController.deleteState);

    // City routes
    app.get('/api/admin/cities', validateToken, cityController.getCities);
    app.post('/api/admin/city', [
        validateToken,
        ...cityValidationRules(),
        sanitize(),
        validate
    ], cityController.addCity);
    app.put('/api/admin/city', [
        validateToken,
        body('city_id').isInt().withMessage('City ID must be an integer'),
        ...cityValidationRules(),
        sanitize(),
        validate
    ], cityController.updateCity);
    app.delete('/api/admin/city/delete', [
        validateToken,
        body('city_id').isInt().withMessage('City ID must be an integer'),
        sanitize(),
        validate
    ], cityController.deleteCity);

    // Member routes
    app.get('/api/admin/members', checkDbConnection, validateToken, memberController.getMembers);
    app.post('/api/admin/member', [
        checkDbConnection,
        validateToken,
        ...memberValidationRules(),
        sanitize(),
        validate
    ], memberController.addMember);
    app.put('/api/admin/member', [
        checkDbConnection,
        validateToken,
        ...memberValidationRules(),
        sanitize(),
        validate
    ], memberController.updateMember);
    app.delete('/api/admin/member/delete', [
        checkDbConnection,
        validateToken,
        body('member_id').isInt().withMessage('Member ID must be an integer'),
        sanitize(),
        validate
    ], memberController.deleteMember);

    // Event routes
    app.post('/api/admin/event', [
        validateToken,
        (req, res, next) => {
            upload.single('event_photo')(req, res, (err) => {
                if (err) {
                    return res.status(400).json({ status: false, message: 'File type not supported.' });
                }
                next();
            });
        },
        ...eventValidationRules(),
        sanitize(),
        validate
    ], eventController.addEvent);
    app.put('/api/admin/event', [
        validateToken,
        (req, res, next) => {
            upload.single('event_photo')(req, res, (err) => {
                if (err) {
                    return res.status(400).json({ status: false, message: 'File type not supported.' });
                }
                next();
            });
        },
        body('event_id').isInt().withMessage('Event ID must be an integer'),
        ...eventValidationRules(),
        sanitize(),
        validate
    ], eventController.updateEvent);
    app.delete('/api/admin/event/delete', [
        validateToken,
        body('event_id').isInt().withMessage('Event ID must be an integer'),
        sanitize(),
        validate
    ], eventController.deleteEvent);
    app.get('/api/admin/events', [
        validateToken
    ], eventController.getEvents);

    // Rules route
    app.get('/api/admin/rules', [
        validateToken
    ], rulesController.getRules);
    app.put('/api/admin/rules', [
        validateToken,
        body('rule_id').isInt().withMessage('Rule ID must be an integer'),
        body('title').notEmpty().withMessage('Title is required'),
        body('description').notEmpty().withMessage('Description is required'),
        sanitize(),
        validate
    ], rulesController.updateRule);

    // About routes
    app.get('/api/admin/about', [
        validateToken
    ], aboutController.getAbout);
    app.put('/api/admin/about', [
        validateToken,
        body('about_id').isInt().withMessage('About ID must be an integer'),
        body('title').notEmpty().withMessage('Title is required'),
        body('description').notEmpty().withMessage('Description is required'),
        sanitize(),
        validate
    ], aboutController.updateAbout);

    // DS City Routes
    app.get('/ds/api/cities', dsCityController.getActiveCities);

    // DS Dashboard Route
    app.get('/ds/api/dashboard', dsDashboardController.getDashboardDetails);

    // DS Rules Route
    app.get('/ds/api/rules', dsRulesController.getRules);

    // DS About Route
    app.get('/ds/api/about', dsAboutController.getAbout);

    // DS Events Route
    app.get('/ds/api/events', dsEventController.getEvents);
    
    // DS Members Route
    app.get('/ds/api/members', dsMemberController.getMembers);
    // DS Family Tree Route
    app.get('/ds/api/family-tree', dsMemberController.getFamilyTree);

    // DS Manage Device Route
    app.post('/ds/api/manage-device', [
        body('device_id').notEmpty().withMessage('Device ID is required'),
        body('device_token').notEmpty().withMessage('Device Token is required'),
        body('device_type').notEmpty().withMessage('Device Type is required'),
        sanitize()
    ], dsManageDeviceController.addOrUpdateDevice);


    // Catch-all route for undefined routes
    app.use((req, res) => {
        res.status(404).json({
            status: false,
            message: "Method not found."
        });
    });

}

module.exports = setRoutes;