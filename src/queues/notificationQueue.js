const Queue = require('bull');
const admin = require('firebase-admin');
const db = require('../db');

// Initialize Firebase Admin SDK
const serviceAccount = require('../config/dabgar-samaj-firebase-service_account.json');
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
});

// Create a Redis-backed queue
const notificationQueue = new Queue('notifications', {
    redis: { host: '127.0.0.1', port: 6379 }, // Update Redis configuration if needed
});

// Process the queue
notificationQueue.process(async (job, done) => {
    const { notificationPayload } = job.data;

    try {
        // Fetch all Android device tokens using promise-based interface
        const connection = await db.getConnection();

        try {
            const [deviceTokens] = await connection.query('SELECT tDeviceToken FROM device_tokens WHERE eDeviceType = "android"');
            connection.release();

            const tokens = deviceTokens.map(token => token.tDeviceToken);

            // Split tokens into batches of 500 (Firebase limit)
            const tokenBatches = [];
            while (tokens.length) {
                tokenBatches.push(tokens.splice(0, 500));
            }

            // Send notifications in batches
            for (const batch of tokenBatches) {
                try {
                    const response = await admin.messaging().sendMulticast({
                        tokens: batch,
                        notification: notificationPayload.notification,
                        data: notificationPayload.data,
                    });

                    // Handle invalid tokens
                    response.responses.forEach((resp, idx) => {
                        if (!resp.success) {
                        }
                    });
                } catch (error) {
                }
            }

            done(); // Mark the job as completed
        } catch (queryErr) {
            connection.release();

            return done(new Error('Database error'));
        }
    } catch (error) {
        done(new Error('Notification processing failed'));
    }
});

module.exports = notificationQueue;
