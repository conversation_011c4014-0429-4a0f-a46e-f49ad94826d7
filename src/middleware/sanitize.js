const { body } = require('express-validator');

const sanitize = () => {
    return [
        body('state_name').trim().escape(),
        body('state_code').trim().escape(),
        body('status').trim().escape(),
        body('city_name').trim().escape(),
        body('city_code').trim().escape(),
        body('vMemberName').trim().escape(),
        body('vMemberNameHi').trim().escape(),
        body('vMemberNameGj').trim().escape(),
        body('vNickName').trim().escape(),
        body('vNickNameHi').trim().escape(),
        body('vNickNameGj').trim().escape(),
        body('vPhoto').trim().escape(),
        body('vMobileNumber').trim().escape(),
        body('vAlternateMobileNumber').trim().escape(),
        body('tAddress').trim().escape(),
        body('vPincode').trim().escape(),
        body('vPanchCity').trim().escape(),
        body('vEmail').trim().escape(),
        body('dDob').trim().escape(),
        body('vGraduation').trim().escape(),
        body('vSchoolCollegeName').trim().escape(),
        body('vHSC').trim().escape(),
        body('vSSC').trim().escape(),
        body('vOccupation').trim().escape(),
        body('vCompanyFermName').trim().escape(),
        body('vExperience').trim().escape(),
        body('vOtherBusiness').trim().escape(),
        body('vChildrenIds').trim().escape(),
        body('vPhysicallyChallengedDetails').trim().escape(),
        body('title').trim().escape(),
        body('description').trim().escape(),
        body('event_title').trim().escape(),
        body('event_date').trim().escape(),
        body('event_description').trim().escape(),
        body('rule_id').trim().escape(),
        body('about_id').trim().escape()
    ];
};

module.exports = sanitize;