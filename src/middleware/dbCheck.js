const checkDbConnection = (req, res, next) => {
    // Skip DB check for OPTIONS requests
    if (req.method === 'OPTIONS') {
        return next();
    }

    try {
        const db = require('../db');

        // Add timeout to prevent hanging
        const checkTimeout = setTimeout(() => {
            console.error('Database check timeout:', {
                method: req.method,
                path: req.path,
                env: process.env.NODE_ENV
            });

            return res.status(503).json({
                status: false,
                message: "Database check timeout",
                details: process.env.NODE_ENV === 'development' ? 'Database connection check took too long' : undefined
            });
        }, 5000); // 5 second timeout

        let isConnected = false;
        try {
            isConnected = db.isConnected();
        } catch (dbErr) {
            clearTimeout(checkTimeout);
            console.error('Database isConnected() error:', dbErr.message);
            return res.status(503).json({
                status: false,
                message: "Database connection check failed",
                details: process.env.NODE_ENV === 'development' ? dbErr.message : undefined
            });
        }

        clearTimeout(checkTimeout);

        if (!isConnected) {
            console.error('Database check failed:', {
                method: req.method,
                path: req.path,
                env: process.env.NODE_ENV,
                dbHost: process.env.DB_HOST,
                connected: isConnected
            });

            return res.status(503).json({
                status: false,
                message: "Database connection not available. Please check your IP address is whitelisted.",
                details: process.env.NODE_ENV === 'development' ? {
                    issue: 'Database not connected',
                    yourIP: '************',
                    action: 'Check database connection and IP whitelist'
                } : undefined
            });
        }

        console.log('✅ Database check passed for', req.method, req.path);
        next();
    } catch (err) {
        console.error('Database module not available:', err.message);
        return res.status(503).json({
            status: false,
            message: "Database service unavailable",
            details: process.env.NODE_ENV === 'development' ? 'Database module failed to load' : undefined
        });
    }
};

module.exports = checkDbConnection;
