const db = require('../db');
const jwt = require('jsonwebtoken');

const validateToken = async (req, res, next) => {
    try {
        // Check for both Bearer token and custom headers for backward compatibility
        let token = null;
        let adminId = null;

        // Check for Bearer token in Authorization header
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7);

            // Verify JWT token
            try {
                const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
                adminId = decoded.id;
                console.log('🔐 JWT token verified for admin ID:', adminId);
            } catch (jwtErr) {
                console.error('❌ JWT verification failed:', jwtErr.message);
                return res.status(401).json({ status: false, message: 'Invalid token' });
            }
        } else {
            // Fallback to custom headers
            const { adminid, token: headerToken } = req.headers;
            if (!adminid || !headerToken) {
                return res.status(401).json({ status: false, message: 'Unauthorized' });
            }
            adminId = adminid;
            token = headerToken;
        }

        if (!adminId || !token) {
            return res.status(401).json({ status: false, message: 'Unauthorized' });
        }

        // Check session in database
        const connection = await db.getConnection();
        console.log('✅ Database connection acquired for token validation');

        try {
            const [results] = await connection.query(
                'SELECT * FROM admin_session WHERE iAdminId = ? AND tToken = ?',
                [adminId, token]
            );
            connection.release();

            if (results.length === 0) {
                console.log('❌ No valid session found for admin ID:', adminId);
                return res.status(401).json({ status: false, message: 'Unauthorized' });
            }

            const session = results[0];
            const currentDate = new Date();

            if (currentDate > new Date(session.dExpiryDate)) {
                console.log('❌ Session expired for admin ID:', adminId);
                return res.status(401).json({ status: false, message: 'Session expired' });
            }

            console.log('✅ Token validation successful for admin ID:', adminId);
            req.adminId = adminId; // Add admin ID to request for use in controllers
            next();

        } catch (queryErr) {
            connection.release();
            console.error('❌ Database error in token validation:', queryErr);
            return res.status(500).json({ status: false, message: 'Database error' });
        }

    } catch (err) {
        console.error('❌ Database connection error in validateToken:', err);
        return res.status(500).json({ status: false, message: 'Database connection error' });
    }
};

module.exports = validateToken;
